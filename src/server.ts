// Fastify 服务器配置
import { log } from 'crawlee';
import Fastify from 'fastify';
import * as cron from 'node-cron';
import { crawlerManager, runCrawler } from './crawler.js';
import { dongcheyundianDataSync } from './fetch-schedule/dongcheyundian/index.js';

// 创建 Fastify 实例
const fastify = Fastify({
  logger: {
    level: 'info',
    transport: {
      target: 'pino-pretty',
      options: {
        colorize: true,
      },
    },
  },
});

// 健康检查端点
fastify.get('/health', async () => {
  return {
    status: 'ok',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
  };
});

// 获取爬虫状态
fastify.get('/api/crawler/status', async () => {
  const status = crawlerManager.getStatus();
  return {
    success: true,
    data: status,
  };
});

// 手动触发爬虫
fastify.post('/api/crawler/run', async (_, reply) => {
  try {
    log.info('收到手动触发爬虫请求');

    // 检查是否已有爬虫在运行
    if (crawlerManager.isRunning()) {
      return reply.code(409).send({
        success: false,
        message: '爬虫正在运行中，请等待当前任务完成',
      });
    }

    // 异步运行爬虫，不阻塞响应
    runCrawler().catch((error) => {
      log.error('爬虫运行失败:', error);
    });

    return {
      success: true,
      message: '爬虫任务已启动',
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    log.error('启动爬虫失败:', { error });
    return reply.code(500).send({
      success: false,
      message: error instanceof Error ? error.message : '启动爬虫失败',
    });
  }
});

// 手动触发懂车云店数据同步
fastify.post('/api/dongcheyundian/datasync', async (_, reply) => {
  try {
    log.info('收到手动触发懂车云店数据同步请求');

    dongcheyundianDataSync().catch((error) => {
      log.error('懂车云店数据同步失败:', error);
    });

    return {
      success: true,
      message: '懂车云店数据同步任务已启动',
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    log.error('懂车云店数据同步失败', { error });
    return reply.code(500).send({
      success: false,
      message: error,
    });
  }
});

/**
 * 设置定时任务
 */
function setupCronJob() {
  // 每日上午 10:00 (中国时区) 执行爬虫任务
  // 表示每天 10 11 12 点执行
  const cronExpression = '0 0 10,11,12 * * *';
  // 测试
  // const cronExpression = '*/15 * * * *';
  cron.schedule(
    cronExpression,
    async () => {
      try {
        log.info('定时任务触发：开始执行爬虫任务');

        // 检查是否已有爬虫在运行
        if (crawlerManager.isRunning()) {
          log.error('定时任务跳过：爬虫正在运行中');
          return;
        }
        await runCrawler();
        log.info('定时任务完成：爬虫执行成功');
      } catch (error) {
        log.error('定时任务失败：爬虫执行出错', { error });
      }
    },
    {
      timezone: 'Asia/Shanghai', // 中国时区
    }
  );
}

/**
 * 启动服务器
 */
export async function startServer(port: number = 3000) {
  try {
    log.info(`正在启动服务器，端口: ${port}`);

    // 设置定时任务
    setupCronJob();

    // 启动服务器
    await fastify.listen({ port, host: '0.0.0.0' });
    log.info(`服务器已启动，监听端口: ${port}`);
    log.info(`健康检查端点: http://0.0.0.0:${port}/health`);
    log.info(`爬虫状态端点: http://0.0.0.0:${port}/api/crawler/status`);

    // 优雅关闭处理
    const gracefulShutdown = async (signal: string) => {
      log.info(`收到 ${signal} 信号，开始优雅关闭...`);
      try {
        await fastify.close();
        log.info('服务器已优雅关闭');
        process.exit(0);
      } catch (error) {
        log.error('关闭服务器时出错:', { error });
        process.exit(1);
      }
    };

    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));
  } catch (error) {
    log.error('服务器启动失败:', { error });
    process.exit(1);
  }
}
