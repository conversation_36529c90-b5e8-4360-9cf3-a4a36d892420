export const msgTableBody = {
  version: 4,
  metaData: {
    appId: 1002649,
  },
  reportId: 48970,
  dataSourceId: 10070,
  query: {
    dataSetId: 2214624,
    dataSetIdList: [2214624],
    fabricBlendingModelInfo: {},
    transform: {
      type: "table",
    },
    groupByIdList: [
      "1700039042948",
      "1700039042904",
      "1700039042891",
      "1700039042872",
      "1700039042875",
      "1700039042876",
      "1700039042874",
      "1700039042896",
    ],
    selectIdList: [],
    fillDateTimeList: [],
    locations: {
      dimensions: [
        "1700039042948",
        "1700039042904",
        "1700039042891",
        "1700039042872",
        "1700039042875",
        "1700039042876",
        "1700039042874",
        "1700039042896",
      ],
      rows: [],
      columns: [],
      tooltips: [],
    },
    dimMetList: [],
    whereList: [
      {
        nodeType: 1,
        op: "and",
        val: [
          {
            name: "日期",
            id: "1700039042872",
            preRelation: "and",
            uniqueId: 250529162139181,
            op: "last",
            option: {
              dateMode: "relative",
              isReportFilter: false,
              isWhereInAggr: true,
            },
            val: [30],
            valOption: {
              anchorOffset: 1,
              datetimeUnit: "day",
              hourSharp: true,
            },
          },
        ],
      },
    ],
    periodCompare: [],
    calculation: {
      trendTable: {},
    },
    limit: 1000,
    pagination: {
      frontOnlyOffset: 10,
      queryKey: "d17b2cc6-ae90-40e5-b40c-79aa91b52000",
      size: 50000,
      offset: 0,
    },
    sort: {},
    topN: null,
    paramList: [],
    cache: {
      cacheVersion: "V1",
      enable: true,
      expire: 300,
    },
    enableNullJoin: false,
    hasDynamicField: false,
    isFirstScreen: false,
    realMetricTableRouteConfig: {
      isRealMetricQuery: false,
    },
    extendQuery: [],
  },
  schema: {
    cache: {
      cacheVersion: "V1",
      enable: true,
      expire: 300,
    },
    colors: [],
    columns: [],
    dimensions: [
      {
        aggrConf: {},
        dimMetId: 1700039042948,
        format: {},
        id: "1700039042948",
        index: 0,
        isGeoField: false,
        isMetric: false,
        location: "dimensions",
        originId: "1700039042948",
        roleType: 0,
        type: "string",
        uniqueId: 250529143646759,
      },
      {
        aggrConf: {},
        dimMetId: 1700039042904,
        format: {},
        id: "1700039042904",
        index: 1,
        isGeoField: false,
        isMetric: false,
        location: "dimensions",
        originId: "1700039042904",
        roleType: 0,
        type: "string",
        uniqueId: 250529143646840,
      },
      {
        aggrConf: {},
        dimMetId: 1700039042891,
        format: {},
        id: "1700039042891",
        index: 2,
        isGeoField: false,
        isMetric: false,
        location: "dimensions",
        originId: "1700039042891",
        roleType: 0,
        type: "string",
        uniqueId: 250529143646958,
      },
      {
        aggrConf: {},
        dimMetId: 1700039042872,
        format: {},
        id: "1700039042872",
        index: 3,
        isGeoField: false,
        isMetric: false,
        location: "dimensions",
        originId: "1700039042872",
        roleType: 0,
        type: "string",
        uniqueId: 250529143646999,
      },
      {
        aggrConf: {},
        dimMetId: 1700039042875,
        format: {
          contentType: "link",
        },
        id: "1700039042875",
        index: 4,
        isGeoField: false,
        isMetric: false,
        location: "dimensions",
        originId: "1700039042875",
        roleType: 0,
        type: "string",
        uniqueId: 250529143647038,
      },
      {
        aggrConf: {},
        dimMetId: 1700039042876,
        format: {
          contentType: "link",
        },
        id: "1700039042876",
        index: 5,
        isGeoField: false,
        isMetric: false,
        location: "dimensions",
        originId: "1700039042876",
        roleType: 0,
        type: "string",
        uniqueId: 250529143647077,
      },
      {
        aggrConf: {},
        dimMetId: 1700039042874,
        format: {},
        id: "1700039042874",
        index: 6,
        isGeoField: false,
        isMetric: false,
        location: "dimensions",
        originId: "1700039042874",
        roleType: 0,
        type: "string",
        uniqueId: 250529162139063,
      },
      {
        aggrConf: {},
        dimMetId: 1700039042896,
        format: {},
        id: "1700039042896",
        index: 7,
        isMetric: false,
        location: "dimensions",
        originId: "1700039042896",
        roleType: 0,
        uniqueId: 250530094820061,
      },
    ],
    display: {
      conf: {
        alignDimension: "left",
        alignMeasure: "right",
        alternateRow: true,
        alternateRowColor: "#FBFBFC",
        autoWrap: false,
        bodyFontBold: false,
        bodyFontColor: "#141414",
        bodyFontItalic: false,
        bodyFontSize: 12,
        bodyFontUnderline: false,
        colPadding: null,
        colSpaceMode: "tight",
        columnWidth: [],
        compact: false,
        compactDirection: "horizontal",
        customFields: {
          enable: true,
        },
        display: "standard",
        fixedIndex: -1,
        gridLineColor: "#E1E4E8",
        gridLineFrame: true,
        gridLineFrameColor: null,
        gridLineFrameStyle: "solid",
        gridLineFrameWidth: 1,
        gridLineHorizontal: true,
        gridLineHorizontalColor: null,
        gridLineHorizontalStyle: "solid",
        gridLineHorizontalWidth: 1,
        gridLineVertical: true,
        gridLineVerticalColor: null,
        gridLineVerticalStyle: "solid",
        gridLineVerticalWidth: 1,
        headerBackground: true,
        headerColor: "#EEF1F5",
        headerFontBold: true,
        headerFontColor: "#1B1F23",
        headerFontItalic: false,
        headerFontSize: 12,
        headerFontUnderline: false,
        headerMenu: true,
        headerSubTitleFontBold: false,
        headerSubTitleFontColor: "rgba(20, 20, 20, 0.45)",
        headerSubTitleFontItalic: false,
        headerSubTitleFontSize: 12,
        headerSubTitleFontUnderline: false,
        hideHeader: false,
        hoverHighlight: "row",
        lineNumber: false,
        loadPartialData: false,
        measureFirst: false,
        pageSize: 10,
        pagination: true,
        rowPadding: null,
        rowSpaceMode: "loose",
        sortable: false,
        specialValue: {
          dimensions: "null",
          measures: "bracketTxt",
        },
        tableStyle: "standard",
        transpose: false,
        version: 33,
      },
      enableAdvisor: true,
      queryType: "table",
      type: "table",
    },
    drill: [],
    extensions: {
      data: {},
      list: [],
      protocolVersion: 1,
    },
    measures: [],
    pagination: {
      frontOnlyOffset: 10,
      queryKey: "d17b2cc6-ae90-40e5-b40c-79aa91b52000",
      size: 50000,
    },
    parameters: [],
    periodCompare: [],
    realMetricTableRouteConfig: {
      isRealMetricQuery: false,
    },
    referenceLine: [],
    reportFilterConfig: {
      layoutSize: "Normal",
      structType: "LeftRight",
    },
    rows: [],
    sizes: [],
    subMeasures: [],
    tableCalculation: {
      rules: [],
    },
    whereList: [
      {
        aggrConf: {},
        dimMetId: 250529162139138,
        filter: {
          children: [
            {
              aggrConf: {},
              dataSetId: 2214624,
              dimMetId: 1700039042872,
              disableMenuKey: ["addOrFilter", "subFilter"],
              filter: {
                op: "last",
                option: {
                  dateMode: "relative",
                  isReportFilter: false,
                  isWhereInAggr: true,
                },
                val: [30],
                valOption: {
                  anchorOffset: 1,
                  datetimeUnit: "day",
                  hourSharp: true,
                },
              },
              format: {},
              id: "1700039042872",
              inCombinationPill: true,
              isMetric: false,
              location: "whereList",
              name: "日期",
              originId: "1700039042872",
              preRelation: "and",
              roleType: 0,
              showEditComponent: true,
              uniqueId: 250529162139181,
              unremovable: true,
            },
          ],
          op: "and",
        },
        format: {
          displayName: "组合筛选",
        },
        highlight: false,
        id: "250529162139138",
        index: 0,
        location: "whereList",
        nameIndex: 1,
        notJoinQuery: false,
        originId: "250529162139138",
        pillType: "combination_filter",
        roleType: 0,
        showEditComponent: false,
        uniqueId: 250529162139138,
        unremovable: false,
      },
    ],
    whiteList: [],
  },
  display: {
    conf: {
      alignDimension: "left",
      alignMeasure: "right",
      alternateRow: true,
      alternateRowColor: "#FBFBFC",
      autoWrap: false,
      bodyFontBold: false,
      bodyFontColor: "#141414",
      bodyFontItalic: false,
      bodyFontSize: 12,
      bodyFontUnderline: false,
      colPadding: null,
      colSpaceMode: "tight",
      columnWidth: [],
      compact: false,
      compactDirection: "horizontal",
      customFields: {
        enable: true,
      },
      display: "standard",
      fixedIndex: -1,
      gridLineColor: "#E1E4E8",
      gridLineFrame: true,
      gridLineFrameColor: null,
      gridLineFrameStyle: "solid",
      gridLineFrameWidth: 1,
      gridLineHorizontal: true,
      gridLineHorizontalColor: null,
      gridLineHorizontalStyle: "solid",
      gridLineHorizontalWidth: 1,
      gridLineVertical: true,
      gridLineVerticalColor: null,
      gridLineVerticalStyle: "solid",
      gridLineVerticalWidth: 1,
      headerBackground: true,
      headerColor: "#EEF1F5",
      headerFontBold: true,
      headerFontColor: "#1B1F23",
      headerFontItalic: false,
      headerFontSize: 12,
      headerFontUnderline: false,
      headerMenu: true,
      headerSubTitleFontBold: false,
      headerSubTitleFontColor: "rgba(20, 20, 20, 0.45)",
      headerSubTitleFontItalic: false,
      headerSubTitleFontSize: 12,
      headerSubTitleFontUnderline: false,
      hideHeader: false,
      hoverHighlight: "row",
      lineNumber: false,
      loadPartialData: false,
      measureFirst: false,
      pageSize: 10,
      pagination: true,
      rowPadding: null,
      rowSpaceMode: "loose",
      sortable: false,
      specialValue: {
        dimensions: "null",
        measures: "bracketTxt",
      },
      tableStyle: "standard",
      transpose: false,
      version: 33,
    },
    enableAdvisor: true,
    queryType: "table",
    type: "table",
    fieldsFormat: {
      "1700039042948": {},
      "1700039042904": {},
      "1700039042891": {},
      "1700039042872": {},
      "1700039042875": {
        contentType: "link",
      },
      "1700039042876": {
        contentType: "link",
      },
      "1700039042874": {},
      "1700039042896": {},
    },
  },
  originalSchema: {
    cache: {
      cacheVersion: "V1",
      enable: true,
      expire: 300,
    },
    colors: [],
    columns: [],
    dimensions: [
      {
        aggrConf: {},
        dimMetId: 1700039042948,
        format: {},
        id: "1700039042948",
        index: 0,
        isGeoField: false,
        isMetric: false,
        location: "dimensions",
        originId: "1700039042948",
        roleType: 0,
        type: "string",
        uniqueId: 250529143646759,
      },
      {
        aggrConf: {},
        dimMetId: 1700039042904,
        format: {},
        id: "1700039042904",
        index: 1,
        isGeoField: false,
        isMetric: false,
        location: "dimensions",
        originId: "1700039042904",
        roleType: 0,
        type: "string",
        uniqueId: 250529143646840,
      },
      {
        aggrConf: {},
        dimMetId: 1700039042891,
        format: {},
        id: "1700039042891",
        index: 2,
        isGeoField: false,
        isMetric: false,
        location: "dimensions",
        originId: "1700039042891",
        roleType: 0,
        type: "string",
        uniqueId: 250529143646958,
      },
      {
        aggrConf: {},
        dimMetId: 1700039042872,
        format: {},
        id: "1700039042872",
        index: 3,
        isGeoField: false,
        isMetric: false,
        location: "dimensions",
        originId: "1700039042872",
        roleType: 0,
        type: "string",
        uniqueId: 250529143646999,
      },
      {
        aggrConf: {},
        dimMetId: 1700039042875,
        format: {
          contentType: "link",
        },
        id: "1700039042875",
        index: 4,
        isGeoField: false,
        isMetric: false,
        location: "dimensions",
        originId: "1700039042875",
        roleType: 0,
        type: "string",
        uniqueId: 250529143647038,
      },
      {
        aggrConf: {},
        dimMetId: 1700039042876,
        format: {
          contentType: "link",
        },
        id: "1700039042876",
        index: 5,
        isGeoField: false,
        isMetric: false,
        location: "dimensions",
        originId: "1700039042876",
        roleType: 0,
        type: "string",
        uniqueId: 250529143647077,
      },
      {
        aggrConf: {},
        dimMetId: 1700039042874,
        format: {},
        id: "1700039042874",
        index: 6,
        isGeoField: false,
        isMetric: false,
        location: "dimensions",
        originId: "1700039042874",
        roleType: 0,
        type: "string",
        uniqueId: 250529162139063,
      },
      {
        aggrConf: {},
        dimMetId: 1700039042896,
        format: {},
        id: "1700039042896",
        index: 7,
        isMetric: false,
        location: "dimensions",
        originId: "1700039042896",
        roleType: 0,
        uniqueId: 250530094820061,
      },
    ],
    display: {
      conf: {
        alignDimension: "left",
        alignMeasure: "right",
        alternateRow: true,
        alternateRowColor: "#FBFBFC",
        autoWrap: false,
        bodyFontBold: false,
        bodyFontColor: "#141414",
        bodyFontItalic: false,
        bodyFontSize: 12,
        bodyFontUnderline: false,
        colPadding: null,
        colSpaceMode: "tight",
        columnWidth: [],
        compact: false,
        compactDirection: "horizontal",
        customFields: {
          enable: true,
        },
        display: "standard",
        fixedIndex: -1,
        gridLineColor: "#E1E4E8",
        gridLineFrame: true,
        gridLineFrameColor: null,
        gridLineFrameStyle: "solid",
        gridLineFrameWidth: 1,
        gridLineHorizontal: true,
        gridLineHorizontalColor: null,
        gridLineHorizontalStyle: "solid",
        gridLineHorizontalWidth: 1,
        gridLineVertical: true,
        gridLineVerticalColor: null,
        gridLineVerticalStyle: "solid",
        gridLineVerticalWidth: 1,
        headerBackground: true,
        headerColor: "#EEF1F5",
        headerFontBold: true,
        headerFontColor: "#1B1F23",
        headerFontItalic: false,
        headerFontSize: 12,
        headerFontUnderline: false,
        headerMenu: true,
        headerSubTitleFontBold: false,
        headerSubTitleFontColor: "rgba(20, 20, 20, 0.45)",
        headerSubTitleFontItalic: false,
        headerSubTitleFontSize: 12,
        headerSubTitleFontUnderline: false,
        hideHeader: false,
        hoverHighlight: "row",
        lineNumber: false,
        loadPartialData: false,
        measureFirst: false,
        pageSize: 10,
        pagination: true,
        rowPadding: null,
        rowSpaceMode: "loose",
        sortable: false,
        specialValue: {
          dimensions: "null",
          measures: "bracketTxt",
        },
        tableStyle: "standard",
        transpose: false,
        version: 33,
      },
      enableAdvisor: true,
      queryType: "table",
      type: "table",
    },
    drill: [],
    extensions: {
      data: {},
      list: [],
      protocolVersion: 1,
    },
    measures: [],
    pagination: {
      frontOnlyOffset: 10,
      queryKey: "d17b2cc6-ae90-40e5-b40c-79aa91b52000",
      size: 50000,
    },
    parameters: [],
    periodCompare: [],
    realMetricTableRouteConfig: {
      isRealMetricQuery: false,
    },
    referenceLine: [],
    reportFilterConfig: {
      layoutSize: "Normal",
      structType: "LeftRight",
    },
    rows: [],
    sizes: [],
    subMeasures: [],
    tableCalculation: {
      rules: [],
    },
    whereList: [
      {
        aggrConf: {},
        dimMetId: 250529162139138,
        filter: {
          children: [
            {
              aggrConf: {},
              dataSetId: 2214624,
              dimMetId: 1700039042872,
              disableMenuKey: ["addOrFilter", "subFilter"],
              filter: {
                op: "last",
                option: {
                  dateMode: "relative",
                  isReportFilter: false,
                  isWhereInAggr: true,
                },
                val: [30],
                valOption: {
                  anchorOffset: 1,
                  datetimeUnit: "day",
                  hourSharp: true,
                },
              },
              format: {},
              id: "1700039042872",
              inCombinationPill: true,
              isMetric: false,
              location: "whereList",
              name: "日期",
              originId: "1700039042872",
              preRelation: "and",
              roleType: 0,
              showEditComponent: true,
              uniqueId: 250529162139181,
              unremovable: true,
            },
          ],
          op: "and",
        },
        format: {
          displayName: "组合筛选",
        },
        highlight: false,
        id: "250529162139138",
        index: 0,
        location: "whereList",
        nameIndex: 1,
        notJoinQuery: false,
        originId: "250529162139138",
        pillType: "combination_filter",
        roleType: 0,
        showEditComponent: false,
        uniqueId: 250529162139138,
        unremovable: false,
      },
    ],
    whiteList: [],
  },
  requestId:
    "aeolus.vizQuery.lp_7442206517828288563_3481485193777450.app_1002649.report_48970.dataset_2214624.93166c3b-e2e6-475e-8c60-9411e5194996",
};
