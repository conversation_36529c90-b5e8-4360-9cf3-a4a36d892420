import { getCheYunDianCookieByBitable } from '../../utils/bitable.js';
import { defaultHeaders } from '../../utils/config.js';
import { importTableData } from '../../utils/import.js';
import { getTokenByCheyundian } from '../../utils/token.js';

import { liveTableBody } from './requestBody/liveTableBody.js';
import { msgTableBody } from './requestBody/msgTableBody.js';
import { postTableBody } from './requestBody/postTableBody.js';

async function getPostListData(token: string) {
  const url =
    'https://aeolus.bytedance.com/aeolus/vqs/openApi/v2/vizQuery/query';
  const body = postTableBody;
  const response = await fetch(url, {
    method: 'POST',
    // @ts-ignore
    headers: {
      ...defaultHeaders,
      'App-Id': 1002649,
      'Content-Language': 'zh-CN',
      'Data-Format-Unit': 'auto',
      'Open-Api-Token': token,
    },
    body: JSON.stringify(body),
  });

  const data = await response.json();
  const postTableDataOrigin = data.data;

  // columnsKey 映射
  const columnsKey = {
    250529142904924: 'nickname',
    250529143646067: 'item_cost',
    250529143646124: 'item_play_count',
    250529143646165: 'item_like_count',
    250529143646206: 'item_comment_count',
    250529143646247: 'item_share_count',
    250529143646294: 'item_id',
    250529143646390: 'item_publish_time',
    250529143646429: 'item_load_message_clue_count',
    250529143646571: 'aweme_id',
    250529143646476: 'item_finish_play_ratio',
    250529160631061: 'user_id',
  };

  const rows = postTableDataOrigin.vizData.datasets?.map((row: string) => {
    const key = Object.keys(row);
    return key.reduce((acc: Record<string, any>, curKey: string) => {
      const mappedKey =
        columnsKey[curKey as unknown as keyof typeof columnsKey];
      if (mappedKey) {
        // @ts-ignore
        acc[mappedKey] = row[curKey];
      }
      return acc;
    }, {});
  });
  // console.log(rows);

  return rows;
}

async function getLiveListData(token: string) {
  const url =
    'https://aeolus.bytedance.com/aeolus/vqs/openApi/v2/vizQuery/query';
  const body = liveTableBody;
  const response = await fetch(url, {
    method: 'POST',
    // @ts-ignore
    headers: {
      ...defaultHeaders,
      'App-Id': 1002649,
      'Content-Language': 'zh-CN',
      'Data-Format-Unit': 'auto',
      'Open-Api-Token': token,
    },
    body: JSON.stringify(body),
  });

  const data = await response.json();
  const postTableDataOrigin = data.data;

  // columnsKey 映射
  const columnsKey = {
    250529140241044: 'roomId',
    250529142904104: 'live_stat_cost',
    250529142904145: 'live_show_uv_by_room',
    250529142904194: 'live_watch_uv_by_room_server',
    250529142904235: 'live_interact_uv_by_room',
    250529142904276: 'live_comment_uv_by_room',
    250529142904319: 'live_like_uv_by_room',
    250529142904362: 'live_share_uv_by_room',
    250529142904403: 'live_icon_card_click_uv',
    250529142904464: 'live_period_feiyu_total_clue_uv',
    250529142904511: 'live_start_time',
    250529142904520: 'live_end_time',
    250529142904601: 'aweme_id',
    250529142904642: 'nickname',
    250529161335061: 'user_id',
    // 250529152017260: '开播日期',
  };

  const rows = postTableDataOrigin.vizData.datasets?.map((row: any) => {
    const key = Object.keys(row);
    return key.reduce((acc, curKey) => {
      // @ts-ignore
      acc[columnsKey[curKey]] = row[curKey];
      return acc;
    }, {});
  });
  // console.log(rows.length);

  return rows;
}

async function getMsgListData(token: string) {
  const url =
    'https://aeolus.bytedance.com/aeolus/vqs/openApi/v2/vizQuery/query';
  const body = msgTableBody;
  const response = await fetch(url, {
    method: 'POST',
    // @ts-ignore
    headers: {
      ...defaultHeaders,
      'App-Id': 1002649,
      'Content-Language': 'zh-CN',
      'Data-Format-Unit': 'auto',
      'Open-Api-Token': token,
    },
    body: JSON.stringify(body),
  });

  const data = await response.json();
  const postTableDataOrigin = data.data;

  // columnsKey 映射
  const columnsKey = {
    250529143646759: 'feiyu_item_clue_count',
    250529143646840: 'reply_message_count_3min',
    250529143646958: 'receive_message_count',
    250529143646999: 'stat_time_day',
    250529143647038: 'aweme_id',
    250529143647077: 'nickname',
    250529162139063: 'user_id',
    250530094820061: 'net_follow_fans',
  };

  const rows = postTableDataOrigin.vizData.datasets?.map((row: string) => {
    const key = Object.keys(row);
    return key.reduce((acc, curKey) => {
      // @ts-ignore
      acc[columnsKey[curKey]] = row[curKey];
      return acc;
    }, {});
  });
  // console.log(rows.length);

  return rows;
}

const importPostTableData = (rowsData: any, url: string, tableName: string) =>
  importTableData(rowsData, url, tableName, '车云店数据同步通知');

async function main() {
  // 1. 从多维表格中的先获取车云店Cookie
  const cheYunDianCookie = await getCheYunDianCookieByBitable();
  // 2. 请求第一个接口获取请求接口需要的鉴权 Token
  const cheYunDianToken = await getTokenByCheyundian(cheYunDianCookie);
  // 使用 Token 来获取数据，发送给后端同步数据

  const postRowsData = await getPostListData(cheYunDianToken);
  const liveRowsData = await getLiveListData(cheYunDianToken);
  const msgRowsData = await getMsgListData(cheYunDianToken);

  await Promise.all([
    importPostTableData(
      postRowsData,
      'https://new-media.xiaofeilun.cn/new-media-api/toyota/short-video/import',
      'Toyota作品BI数据表'
    ),
    importPostTableData(
      liveRowsData,
      'https://new-media.xiaofeilun.cn/new-media-api/toyota/live/import',
      'Toyota直播BI数据表'
    ),
    importPostTableData(
      msgRowsData,
      'https://new-media.xiaofeilun.cn/new-media-api/toyota/private-message/import',
      'Toyota私信BI数据表'
    ),
  ]);
}

main();
