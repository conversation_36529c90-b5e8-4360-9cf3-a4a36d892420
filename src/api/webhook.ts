const webhookUrl =
  'https://open.feishu.cn/open-apis/bot/v2/hook/051fac08-4477-4dd2-8214-b43548eef292';

export const sendMessage = async (
  title: string = '标题',
  content: string,
  type: 'success' | 'fail',
  isDev?: boolean
) => {
  const cardMessage = {
    msg_type: 'interactive',
    card: {
      schema: '2.0',
      config: {
        update_multi: true,
        style: {
          text_size: {
            normal_v2: {
              default: 'normal',
              pc: 'normal',
              mobile: 'heading',
            },
          },
        },
      },
      body: {
        direction: 'vertical',
        padding: '12px 12px 12px 12px',
        elements: [
          {
            tag: 'div',
            text: {
              content: content,
              tag: 'lark_md',
            },
          },
        ],
      },
      header: {
        title: {
          tag: 'plain_text',
          content: title,
        },
        template: isDev ? 'orange' : type === 'success' ? 'blue' : 'red',
        padding: '12px 12px 12px 12px',
      },
    },
  };

  const response = await fetch(webhookUrl, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(cardMessage),
  });

  const data = await response.json();
  console.log(data);
};
