export const postTableBody = {
    "version": 4,
    "metaData": {
        "appId": 1002649
    },
    "reportId": 50038,
    "dataSourceId": 10070,
    "query": {
        "dataSetId": 2213514,
        "dataSetIdList": [
            2213514
        ],
        "fabricBlendingModelInfo": {},
        "transform": {
            "type": "table"
        },
        "groupByIdList": [
            "1700039018970",
            "1700039018974",
            "1700039018960",
            "1700039018961",
            "1700039018958"
        ],
        "selectIdList": [
            "sum_1700039018972",
            "sum_1700039018977",
            "sum_1700039018979",
            "sum_1700039018978",
            "sum_1700039018980"
        ],
        "fillDateTimeList": [],
        "locations": {
            "dimensions": [
                "1700039018970",
                "1700039018974",
                "1700039018960",
                "1700039018961",
                "1700039018958"
            ],
            "rows": [],
            "columns": [],
            "tooltips": []
        },
        "dimMetList": [
            {
                "id": "sum_1700039018972",
                "originId": "1700039018972",
                "dimMetId": 1700039018972,
                "uniqueId": 250616164342291,
                "name": "视频时长(秒)",
                "expr": "item_duration",
                "fullExpr": "item_duration",
                "roleType": 1,
                "scope": 0,
                "dataType": "int",
                "isRaw": false,
                "mapKey": null,
                "aggregation": {
                    "exprAggr": "sum("
                },
                "sourceType": "aggr",
                "persisted": false,
                "ownerEmailPrefix": "pengmingguo",
                "dataSetId": 2213514
            },
            {
                "id": "sum_1700039018977",
                "originId": "1700039018977",
                "dimMetId": 1700039018977,
                "uniqueId": 250616164342316,
                "name": "播放量",
                "expr": "item_play_count",
                "fullExpr": "item_play_count",
                "roleType": 1,
                "scope": 0,
                "dataType": "int",
                "isRaw": false,
                "mapKey": null,
                "aggregation": {
                    "exprAggr": "sum("
                },
                "sourceType": "aggr",
                "persisted": false,
                "ownerEmailPrefix": "pengmingguo",
                "dataSetId": 2213514
            },
            {
                "id": "sum_1700039018979",
                "originId": "1700039018979",
                "dimMetId": 1700039018979,
                "uniqueId": 250616164342341,
                "name": "评论量",
                "expr": "item_comment_count",
                "fullExpr": "item_comment_count",
                "roleType": 1,
                "scope": 0,
                "dataType": "int",
                "isRaw": false,
                "mapKey": null,
                "aggregation": {
                    "exprAggr": "sum("
                },
                "sourceType": "aggr",
                "persisted": false,
                "ownerEmailPrefix": "pengmingguo",
                "dataSetId": 2213514
            },
            {
                "id": "sum_1700039018978",
                "originId": "1700039018978",
                "dimMetId": 1700039018978,
                "uniqueId": 250616164342350,
                "name": "点赞量",
                "expr": "item_like_count",
                "fullExpr": "item_like_count",
                "roleType": 1,
                "scope": 0,
                "dataType": "int",
                "isRaw": false,
                "mapKey": null,
                "aggregation": {
                    "exprAggr": "sum("
                },
                "sourceType": "aggr",
                "persisted": false,
                "ownerEmailPrefix": "pengmingguo",
                "dataSetId": 2213514
            },
            {
                "id": "sum_1700039018980",
                "originId": "1700039018980",
                "dimMetId": 1700039018980,
                "uniqueId": 250616164342375,
                "name": "分享量",
                "expr": "item_share_count",
                "fullExpr": "item_share_count",
                "roleType": 1,
                "scope": 0,
                "dataType": "int",
                "isRaw": false,
                "mapKey": null,
                "aggregation": {
                    "exprAggr": "sum("
                },
                "sourceType": "aggr",
                "persisted": false,
                "ownerEmailPrefix": "pengmingguo",
                "dataSetId": 2213514
            }
        ],
        "whereList": [
            {
                "nodeType": 1,
                "op": "and",
                "val": [
                    {
                        "name": "数据表现日期",
                        "id": "1700039018957",
                        "preRelation": "and",
                        "uniqueId": 250616164342267,
                        "op": "last",
                        "option": {
                            "isReportFilter": false,
                            "dateMode": "relative",
                            "isWhereInAggr": true
                        },
                        "val": [
                            1
                        ],
                        "valOption": {
                            "datetimeUnit": "day",
                            "hourSharp": true,
                            "anchorOffset": 1
                        }
                    },
                    {
                        "name": "发布日期",
                        "id": "1700039018955",
                        "preRelation": "and",
                        "uniqueId": 250616164342268,
                        "op": "last",
                        "option": {
                            "isReportFilter": false,
                            "dateMode": "relative",
                            "isWhereInAggr": true
                        },
                        "val": [
                            3
                        ],
                        "valOption": {
                            "datetimeUnit": "month",
                            "hourSharp": true,
                            "until": "yesterday",
                            "anchorOffset": 0
                        }
                    }
                ]
            },
            {
                "name": "作者抖音昵称",
                "id": "1700039018961",
                "preRelation": "and",
                "uniqueId": 250616164342605,
                "op": "not in",
                "option": {
                    "filterPattern": "Accurate",
                    "isReportFilter": false,
                    "isWhereInAggr": true,
                    "displayType": "multiDropDownList",
                    "desensitizationList": [],
                    "customList": []
                },
                "val": [
                    "车宇宙助手"
                ],
                "valOption": {}
            }
        ],
        "periodCompare": [],
        "calculation": {
            "trendTable": {}
        },
        "limit": 1000,
        "sort": {},
        "topN": null,
        "paramList": [],
        "cache": {
            "enable": true,
            "cacheVersion": "V1",
            "expire": 300
        },
        "enableNullJoin": false,
        "hasDynamicField": false,
        "isFirstScreen": false,
        "realMetricTableRouteConfig": {
            "isRealMetricQuery": false
        },
        "extendQuery": []
    },
    "schema": {
        "rows": [],
        "reportFilterConfig": {
            "structType": "LeftRight",
            "layoutSize": "Normal"
        },
        "dimensions": [
            {
                "roleType": 0,
                "index": 0,
                "format": {
                    "contentType": "link"
                },
                "aggrConf": {},
                "isMetric": false,
                "dimMetId": 1700039018970,
                "location": "dimensions",
                "uniqueId": 250616164342037,
                "isGeoField": false,
                "type": "string",
                "id": "1700039018970",
                "originId": "1700039018970"
            },
            {
                "roleType": 0,
                "index": 1,
                "format": {},
                "aggrConf": {},
                "isMetric": false,
                "dimMetId": 1700039018974,
                "location": "dimensions",
                "uniqueId": 250616164342072,
                "isGeoField": false,
                "type": "string",
                "id": "1700039018974",
                "originId": "1700039018974"
            },
            {
                "roleType": 0,
                "index": 2,
                "format": {
                    "contentType": "link"
                },
                "aggrConf": {},
                "isMetric": false,
                "dimMetId": 1700039018960,
                "location": "dimensions",
                "uniqueId": 250616164342551,
                "isGeoField": false,
                "type": "string",
                "id": "1700039018960",
                "originId": "1700039018960"
            },
            {
                "roleType": 0,
                "index": 3,
                "format": {
                    "contentType": "link"
                },
                "aggrConf": {},
                "isMetric": false,
                "dimMetId": 1700039018961,
                "location": "dimensions",
                "uniqueId": 250616164342580,
                "isGeoField": false,
                "type": "string",
                "id": "1700039018961",
                "originId": "1700039018961"
            },
            {
                "uniqueId": 250616174956089,
                "id": "1700039018958",
                "location": "dimensions",
                "dimMetId": 1700039018958,
                "originId": "1700039018958",
                "roleType": 0,
                "aggrConf": {},
                "format": {},
                "isMetric": false,
                "index": 4,
                "type": "string",
                "isGeoField": false
            }
        ],
        "parameters": [],
        "sizes": [],
        "whereList": [
            {
                "roleType": 0,
                "index": 0,
                "unremovable": false,
                "format": {
                    "displayName": "组合筛选"
                },
                "aggrConf": {},
                "id": "250616164342242",
                "notJoinQuery": false,
                "dimMetId": 250616164342242,
                "filter": {
                    "children": [
                        {
                            "roleType": 0,
                            "filter": {
                                "op": "last",
                                "option": {
                                    "isReportFilter": false,
                                    "dateMode": "relative",
                                    "isWhereInAggr": true
                                },
                                "val": [
                                    1
                                ],
                                "valOption": {
                                    "datetimeUnit": "day",
                                    "hourSharp": true,
                                    "anchorOffset": 1
                                }
                            },
                            "unremovable": true,
                            "name": "数据表现日期",
                            "format": {},
                            "aggrConf": {},
                            "dataSetId": 2213514,
                            "dimMetId": 1700039018957,
                            "disableMenuKey": [
                                "addOrFilter",
                                "subFilter"
                            ],
                            "preRelation": "and",
                            "location": "whereList",
                            "uniqueId": 250616164342267,
                            "showEditComponent": true,
                            "inCombinationPill": true,
                            "isMetric": false,
                            "id": "1700039018957",
                            "originId": "1700039018957"
                        },
                        {
                            "roleType": 0,
                            "filter": {
                                "op": "last",
                                "option": {
                                    "isReportFilter": false,
                                    "dateMode": "relative",
                                    "isWhereInAggr": true
                                },
                                "val": [
                                    3
                                ],
                                "valOption": {
                                    "datetimeUnit": "month",
                                    "hourSharp": true,
                                    "until": "yesterday",
                                    "anchorOffset": 0
                                }
                            },
                            "unremovable": true,
                            "name": "发布日期",
                            "format": {},
                            "aggrConf": {},
                            "dataSetId": 2213514,
                            "dimMetId": 1700039018955,
                            "disableMenuKey": [
                                "addOrFilter",
                                "subFilter"
                            ],
                            "preRelation": "and",
                            "location": "whereList",
                            "uniqueId": 250616164342268,
                            "showEditComponent": true,
                            "inCombinationPill": true,
                            "isMetric": false,
                            "id": "1700039018955",
                            "originId": "1700039018955"
                        }
                    ],
                    "op": "and"
                },
                "location": "whereList",
                "uniqueId": 250616164342242,
                "highlight": false,
                "pillType": "combination_filter",
                "showEditComponent": false,
                "nameIndex": 1,
                "originId": "250616164342242"
            },
            {
                "roleType": 0,
                "index": 1,
                "name": "作者抖音昵称",
                "format": {
                    "contentType": "link"
                },
                "aggrConf": {},
                "dataSetId": 2213514,
                "dimMetId": 1700039018961,
                "filter": {
                    "op": "not in",
                    "option": {
                        "filterPattern": "Accurate",
                        "isReportFilter": false,
                        "isWhereInAggr": true,
                        "displayType": "multiDropDownList",
                        "desensitizationList": [],
                        "customList": []
                    },
                    "val": [
                        "车宇宙助手"
                    ],
                    "valOption": {}
                },
                "isRequired": false,
                "preRelation": "and",
                "location": "whereList",
                "uniqueId": 250616164342605,
                "showEditComponent": false,
                "isMetric": false,
                "id": "1700039018961",
                "originId": "1700039018961"
            }
        ],
        "whiteList": [],
        "measures": [
            {
                "roleType": 1,
                "index": 0,
                "format": {},
                "aggrConf": {
                    "exprAggr": "sum("
                },
                "isMetric": false,
                "dimMetId": 1700039018972,
                "location": "measures",
                "uniqueId": 250616164342291,
                "isGeoField": false,
                "type": "string",
                "id": "sum_1700039018972",
                "originId": "1700039018972"
            },
            {
                "roleType": 1,
                "index": 1,
                "format": {},
                "aggrConf": {
                    "exprAggr": "sum("
                },
                "isMetric": false,
                "dimMetId": 1700039018977,
                "location": "measures",
                "uniqueId": 250616164342316,
                "isGeoField": false,
                "type": "string",
                "id": "sum_1700039018977",
                "originId": "1700039018977"
            },
            {
                "roleType": 1,
                "index": 2,
                "format": {},
                "aggrConf": {
                    "exprAggr": "sum("
                },
                "isMetric": false,
                "dimMetId": 1700039018979,
                "location": "measures",
                "uniqueId": 250616164342341,
                "isGeoField": false,
                "type": "string",
                "id": "sum_1700039018979",
                "originId": "1700039018979"
            },
            {
                "roleType": 1,
                "index": 3,
                "format": {},
                "aggrConf": {
                    "exprAggr": "sum("
                },
                "isMetric": false,
                "dimMetId": 1700039018978,
                "location": "measures",
                "uniqueId": 250616164342350,
                "isGeoField": false,
                "type": "string",
                "id": "sum_1700039018978",
                "originId": "1700039018978"
            },
            {
                "roleType": 1,
                "index": 4,
                "format": {},
                "aggrConf": {
                    "exprAggr": "sum("
                },
                "isMetric": false,
                "dimMetId": 1700039018980,
                "location": "measures",
                "uniqueId": 250616164342375,
                "isGeoField": false,
                "type": "string",
                "id": "sum_1700039018980",
                "originId": "1700039018980"
            }
        ],
        "periodCompare": [],
        "display": {
            "conf": {
                "bodyFontUnderline": false,
                "fixedIndex": -1,
                "headerFontColor": "#1B1F23",
                "headerColor": "#EEF1F5",
                "bodyFontSize": 12,
                "version": 33,
                "pageSize": 20,
                "headerFontUnderline": false,
                "colPadding": null,
                "gridLineFrameWidth": 1,
                "headerFontSize": 12,
                "headerSubTitleFontSize": 12,
                "alternateRowColor": "#FBFBFC",
                "headerBackground": true,
                "compact": false,
                "rowPadding": null,
                "headerSubTitleFontItalic": false,
                "gridLineFrameStyle": "solid",
                "alignMeasure": "right",
                "headerSubTitleFontUnderline": false,
                "autoWrap": false,
                "gridLineVerticalWidth": 1,
                "headerSubTitleFontBold": false,
                "tableStyle": "standard",
                "pagination": false,
                "specialValue": {
                    "measures": "bracketTxt",
                    "dimensions": "null"
                },
                "customFields": {
                    "enable": true
                },
                "gridLineHorizontal": true,
                "headerFontItalic": false,
                "gridLineFrame": true,
                "bodyFontItalic": false,
                "colSpaceMode": "tight",
                "gridLineVerticalColor": null,
                "bodyFontColor": "#141414",
                "rowSpaceMode": "loose",
                "gridLineFrameColor": null,
                "alternateRow": true,
                "gridLineHorizontalStyle": "solid",
                "headerFontBold": true,
                "loadPartialData": true,
                "transpose": false,
                "lineNumber": false,
                "gridLineHorizontalColor": null,
                "hideHeader": false,
                "compactDirection": "horizontal",
                "gridLineColor": "#E1E4E8",
                "gridLineVerticalStyle": "solid",
                "sortable": false,
                "measureFirst": false,
                "columnWidth": [],
                "headerSubTitleFontColor": "rgba(20, 20, 20, 0.45)",
                "gridLineHorizontalWidth": 1,
                "headerMenu": true,
                "alignDimension": "left",
                "hoverHighlight": "row",
                "bodyFontBold": false,
                "gridLineVertical": true,
                "display": "standard"
            },
            "type": "table",
            "queryType": "table",
            "enableAdvisor": true
        },
        "cache": {
            "enable": true,
            "cacheVersion": "V1",
            "expire": 300
        },
        "colors": [],
        "extensions": {
            "data": {},
            "list": [],
            "protocolVersion": 1
        },
        "drill": [],
        "referenceLine": [],
        "realMetricTableRouteConfig": {
            "isRealMetricQuery": false
        },
        "subMeasures": [],
        "annotation": {
            "hash": "d751713988987e9331980363e24189ce"
        },
        "columns": [],
        "tableCalculation": {
            "rules": []
        }
    },
    "display": {
        "conf": {
            "bodyFontUnderline": false,
            "fixedIndex": -1,
            "headerFontColor": "#1B1F23",
            "headerColor": "#EEF1F5",
            "bodyFontSize": 12,
            "version": 33,
            "pageSize": 20,
            "headerFontUnderline": false,
            "colPadding": null,
            "gridLineFrameWidth": 1,
            "headerFontSize": 12,
            "headerSubTitleFontSize": 12,
            "alternateRowColor": "#FBFBFC",
            "headerBackground": true,
            "compact": false,
            "rowPadding": null,
            "headerSubTitleFontItalic": false,
            "gridLineFrameStyle": "solid",
            "alignMeasure": "right",
            "headerSubTitleFontUnderline": false,
            "autoWrap": false,
            "gridLineVerticalWidth": 1,
            "headerSubTitleFontBold": false,
            "tableStyle": "standard",
            "pagination": false,
            "specialValue": {
                "measures": "bracketTxt",
                "dimensions": "null"
            },
            "customFields": {
                "enable": true
            },
            "gridLineHorizontal": true,
            "headerFontItalic": false,
            "gridLineFrame": true,
            "bodyFontItalic": false,
            "colSpaceMode": "tight",
            "gridLineVerticalColor": null,
            "bodyFontColor": "#141414",
            "rowSpaceMode": "loose",
            "gridLineFrameColor": null,
            "alternateRow": true,
            "gridLineHorizontalStyle": "solid",
            "headerFontBold": true,
            "loadPartialData": true,
            "transpose": false,
            "lineNumber": false,
            "gridLineHorizontalColor": null,
            "hideHeader": false,
            "compactDirection": "horizontal",
            "gridLineColor": "#E1E4E8",
            "gridLineVerticalStyle": "solid",
            "sortable": false,
            "measureFirst": false,
            "columnWidth": [],
            "headerSubTitleFontColor": "rgba(20, 20, 20, 0.45)",
            "gridLineHorizontalWidth": 1,
            "headerMenu": true,
            "alignDimension": "left",
            "hoverHighlight": "row",
            "bodyFontBold": false,
            "gridLineVertical": true,
            "display": "standard"
        },
        "type": "table",
        "queryType": "table",
        "enableAdvisor": true,
        "fieldsFormat": {
            "1700039018970": {
                "contentType": "link"
            },
            "1700039018974": {},
            "1700039018960": {
                "contentType": "link"
            },
            "1700039018961": {
                "contentType": "link"
            },
            "1700039018958": {},
            "sum_1700039018972": {},
            "sum_1700039018977": {},
            "sum_1700039018979": {},
            "sum_1700039018978": {},
            "sum_1700039018980": {}
        }
    },
    "originalSchema": {
        "rows": [],
        "reportFilterConfig": {
            "structType": "LeftRight",
            "layoutSize": "Normal"
        },
        "dimensions": [
            {
                "roleType": 0,
                "index": 0,
                "format": {
                    "contentType": "link"
                },
                "aggrConf": {},
                "isMetric": false,
                "dimMetId": 1700039018970,
                "location": "dimensions",
                "uniqueId": 250616164342037,
                "isGeoField": false,
                "type": "string",
                "id": "1700039018970",
                "originId": "1700039018970"
            },
            {
                "roleType": 0,
                "index": 1,
                "format": {},
                "aggrConf": {},
                "isMetric": false,
                "dimMetId": 1700039018974,
                "location": "dimensions",
                "uniqueId": 250616164342072,
                "isGeoField": false,
                "type": "string",
                "id": "1700039018974",
                "originId": "1700039018974"
            },
            {
                "roleType": 0,
                "index": 2,
                "format": {
                    "contentType": "link"
                },
                "aggrConf": {},
                "isMetric": false,
                "dimMetId": 1700039018960,
                "location": "dimensions",
                "uniqueId": 250616164342551,
                "isGeoField": false,
                "type": "string",
                "id": "1700039018960",
                "originId": "1700039018960"
            },
            {
                "roleType": 0,
                "index": 3,
                "format": {
                    "contentType": "link"
                },
                "aggrConf": {},
                "isMetric": false,
                "dimMetId": 1700039018961,
                "location": "dimensions",
                "uniqueId": 250616164342580,
                "isGeoField": false,
                "type": "string",
                "id": "1700039018961",
                "originId": "1700039018961"
            },
            {
                "uniqueId": 250616174956089,
                "id": "1700039018958",
                "location": "dimensions",
                "dimMetId": 1700039018958,
                "originId": "1700039018958",
                "roleType": 0,
                "aggrConf": {},
                "format": {},
                "isMetric": false,
                "index": 4,
                "type": "string",
                "isGeoField": false
            }
        ],
        "parameters": [],
        "sizes": [],
        "whereList": [
            {
                "roleType": 0,
                "index": 0,
                "unremovable": false,
                "format": {
                    "displayName": "组合筛选"
                },
                "aggrConf": {},
                "id": "250616164342242",
                "notJoinQuery": false,
                "dimMetId": 250616164342242,
                "filter": {
                    "children": [
                        {
                            "roleType": 0,
                            "filter": {
                                "op": "last",
                                "option": {
                                    "isReportFilter": false,
                                    "dateMode": "relative",
                                    "isWhereInAggr": true
                                },
                                "val": [
                                    1
                                ],
                                "valOption": {
                                    "datetimeUnit": "day",
                                    "hourSharp": true,
                                    "anchorOffset": 1
                                }
                            },
                            "unremovable": true,
                            "name": "数据表现日期",
                            "format": {},
                            "aggrConf": {},
                            "dataSetId": 2213514,
                            "dimMetId": 1700039018957,
                            "disableMenuKey": [
                                "addOrFilter",
                                "subFilter"
                            ],
                            "preRelation": "and",
                            "location": "whereList",
                            "uniqueId": 250616164342267,
                            "showEditComponent": true,
                            "inCombinationPill": true,
                            "isMetric": false,
                            "id": "1700039018957",
                            "originId": "1700039018957"
                        },
                        {
                            "roleType": 0,
                            "filter": {
                                "op": "last",
                                "option": {
                                    "isReportFilter": false,
                                    "dateMode": "relative",
                                    "isWhereInAggr": true
                                },
                                "val": [
                                    3
                                ],
                                "valOption": {
                                    "datetimeUnit": "month",
                                    "hourSharp": true,
                                    "until": "yesterday",
                                    "anchorOffset": 0
                                }
                            },
                            "unremovable": true,
                            "name": "发布日期",
                            "format": {},
                            "aggrConf": {},
                            "dataSetId": 2213514,
                            "dimMetId": 1700039018955,
                            "disableMenuKey": [
                                "addOrFilter",
                                "subFilter"
                            ],
                            "preRelation": "and",
                            "location": "whereList",
                            "uniqueId": 250616164342268,
                            "showEditComponent": true,
                            "inCombinationPill": true,
                            "isMetric": false,
                            "id": "1700039018955",
                            "originId": "1700039018955"
                        }
                    ],
                    "op": "and"
                },
                "location": "whereList",
                "uniqueId": 250616164342242,
                "highlight": false,
                "pillType": "combination_filter",
                "showEditComponent": false,
                "nameIndex": 1,
                "originId": "250616164342242"
            },
            {
                "roleType": 0,
                "index": 1,
                "name": "作者抖音昵称",
                "format": {
                    "contentType": "link"
                },
                "aggrConf": {},
                "dataSetId": 2213514,
                "dimMetId": 1700039018961,
                "filter": {
                    "op": "not in",
                    "option": {
                        "filterPattern": "Accurate",
                        "isReportFilter": false,
                        "isWhereInAggr": true,
                        "displayType": "multiDropDownList",
                        "desensitizationList": [],
                        "customList": []
                    },
                    "val": [
                        "车宇宙助手"
                    ],
                    "valOption": {}
                },
                "isRequired": false,
                "preRelation": "and",
                "location": "whereList",
                "uniqueId": 250616164342605,
                "showEditComponent": false,
                "isMetric": false,
                "id": "1700039018961",
                "originId": "1700039018961"
            }
        ],
        "whiteList": [],
        "measures": [
            {
                "roleType": 1,
                "index": 0,
                "format": {},
                "aggrConf": {
                    "exprAggr": "sum("
                },
                "isMetric": false,
                "dimMetId": 1700039018972,
                "location": "measures",
                "uniqueId": 250616164342291,
                "isGeoField": false,
                "type": "string",
                "id": "sum_1700039018972",
                "originId": "1700039018972"
            },
            {
                "roleType": 1,
                "index": 1,
                "format": {},
                "aggrConf": {
                    "exprAggr": "sum("
                },
                "isMetric": false,
                "dimMetId": 1700039018977,
                "location": "measures",
                "uniqueId": 250616164342316,
                "isGeoField": false,
                "type": "string",
                "id": "sum_1700039018977",
                "originId": "1700039018977"
            },
            {
                "roleType": 1,
                "index": 2,
                "format": {},
                "aggrConf": {
                    "exprAggr": "sum("
                },
                "isMetric": false,
                "dimMetId": 1700039018979,
                "location": "measures",
                "uniqueId": 250616164342341,
                "isGeoField": false,
                "type": "string",
                "id": "sum_1700039018979",
                "originId": "1700039018979"
            },
            {
                "roleType": 1,
                "index": 3,
                "format": {},
                "aggrConf": {
                    "exprAggr": "sum("
                },
                "isMetric": false,
                "dimMetId": 1700039018978,
                "location": "measures",
                "uniqueId": 250616164342350,
                "isGeoField": false,
                "type": "string",
                "id": "sum_1700039018978",
                "originId": "1700039018978"
            },
            {
                "roleType": 1,
                "index": 4,
                "format": {},
                "aggrConf": {
                    "exprAggr": "sum("
                },
                "isMetric": false,
                "dimMetId": 1700039018980,
                "location": "measures",
                "uniqueId": 250616164342375,
                "isGeoField": false,
                "type": "string",
                "id": "sum_1700039018980",
                "originId": "1700039018980"
            }
        ],
        "periodCompare": [],
        "display": {
            "conf": {
                "bodyFontUnderline": false,
                "fixedIndex": -1,
                "headerFontColor": "#1B1F23",
                "headerColor": "#EEF1F5",
                "bodyFontSize": 12,
                "version": 33,
                "pageSize": 20,
                "headerFontUnderline": false,
                "colPadding": null,
                "gridLineFrameWidth": 1,
                "headerFontSize": 12,
                "headerSubTitleFontSize": 12,
                "alternateRowColor": "#FBFBFC",
                "headerBackground": true,
                "compact": false,
                "rowPadding": null,
                "headerSubTitleFontItalic": false,
                "gridLineFrameStyle": "solid",
                "alignMeasure": "right",
                "headerSubTitleFontUnderline": false,
                "autoWrap": false,
                "gridLineVerticalWidth": 1,
                "headerSubTitleFontBold": false,
                "tableStyle": "standard",
                "pagination": false,
                "specialValue": {
                    "measures": "bracketTxt",
                    "dimensions": "null"
                },
                "customFields": {
                    "enable": true
                },
                "gridLineHorizontal": true,
                "headerFontItalic": false,
                "gridLineFrame": true,
                "bodyFontItalic": false,
                "colSpaceMode": "tight",
                "gridLineVerticalColor": null,
                "bodyFontColor": "#141414",
                "rowSpaceMode": "loose",
                "gridLineFrameColor": null,
                "alternateRow": true,
                "gridLineHorizontalStyle": "solid",
                "headerFontBold": true,
                "loadPartialData": true,
                "transpose": false,
                "lineNumber": false,
                "gridLineHorizontalColor": null,
                "hideHeader": false,
                "compactDirection": "horizontal",
                "gridLineColor": "#E1E4E8",
                "gridLineVerticalStyle": "solid",
                "sortable": false,
                "measureFirst": false,
                "columnWidth": [],
                "headerSubTitleFontColor": "rgba(20, 20, 20, 0.45)",
                "gridLineHorizontalWidth": 1,
                "headerMenu": true,
                "alignDimension": "left",
                "hoverHighlight": "row",
                "bodyFontBold": false,
                "gridLineVertical": true,
                "display": "standard"
            },
            "type": "table",
            "queryType": "table",
            "enableAdvisor": true
        },
        "cache": {
            "enable": true,
            "cacheVersion": "V1",
            "expire": 300
        },
        "colors": [],
        "extensions": {
            "data": {},
            "list": [],
            "protocolVersion": 1
        },
        "drill": [],
        "referenceLine": [],
        "realMetricTableRouteConfig": {
            "isRealMetricQuery": false
        },
        "subMeasures": [],
        "annotation": {
            "hash": "d751713988987e9331980363e24189ce"
        },
        "columns": [],
        "tableCalculation": {
            "rules": []
        }
    },
}