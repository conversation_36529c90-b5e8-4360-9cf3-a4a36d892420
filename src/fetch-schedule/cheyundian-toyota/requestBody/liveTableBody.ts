export const liveTableBody = {
  version: 4,
  metaData: {
    appId: 1002649,
  },
  reportId: 52634,
  dataSourceId: 10070,
  query: {
    dataSetId: 2211620,
    dataSetIdList: [2211620],
    fabricBlendingModelInfo: {},
    transform: {
      type: 'table',
    },
    groupByIdList: [
      '1700038977648',
      '1700038977695',
      '1700038977696',
      '1700038977644',
      '1700038977689',
      '1700038979296',
      '1700038981733',
      '1700038977681',
      '1700038977662',
      '1700038977658',
    ],
    selectIdList: ['1700039098260'],
    fillDateTimeList: [],
    locations: {
      dimensions: [
        '1700038977648',
        '1700038977695',
        '1700038977696',
        '1700038977644',
        '1700038977689',
        '1700038979296',
        '1700038981733',
        '1700038977681',
        '1700038977662',
        '1700038977658',
      ],
      rows: [],
      columns: [],
      tooltips: [],
    },
    dimMetList: [],
    whereList: [
      {
        nodeType: 1,
        op: 'and',
        val: [
          {
            name: '开播日期',
            id: '1700038977644',
            preRelation: 'and',
            uniqueId: 250729192312446,
            op: 'last',
            option: {
              isReportFilter: false,
              dateMode: 'relative',
              isWhereInAggr: true,
            },
            val: [1],
            valOption: {
              datetimeUnit: 'month',
              hourSharp: true,
              until: 'yesterday',
              anchorOffset: 0,
            },
          },
        ],
      },
      {
        name: '开播抖音号',
        id: '1700038977695',
        preRelation: 'and',
        uniqueId: 250729203208051,
        op: 'not like',
        option: {
          isReportFilter: false,
          filterPattern: 'Condition',
        },
        val: ['89020762080', '55277644120'],
        valOption: {
          fuzzyLikePattern: 'Contain',
        },
      },
    ],
    periodCompare: [],
    calculation: {
      trendTable: {},
    },
    limit: 1000,
    sort: {},
    topN: null,
    paramList: [],
    cache: {
      enable: true,
      cacheVersion: 'V1',
      expire: 300,
    },
    enableNullJoin: false,
    hasDynamicField: false,
    isFirstScreen: false,
    realMetricTableRouteConfig: {
      isRealMetricQuery: false,
    },
    extendQuery: [],
  },
  schema: {
    rows: [],
    reportFilterConfig: {
      structType: 'LeftRight',
      layoutSize: 'Normal',
    },
    dimensions: [
      {
        roleType: 0,
        index: 0,
        format: {},
        aggrConf: {},
        isMetric: false,
        dimMetId: 1700038977648,
        location: 'dimensions',
        uniqueId: 250729192312039,
        isGeoField: false,
        type: 'string',
        id: '1700038977648',
        originId: '1700038977648',
      },
      {
        roleType: 0,
        index: 1,
        format: {
          contentType: 'link',
        },
        aggrConf: {},
        isMetric: false,
        dimMetId: 1700038977695,
        location: 'dimensions',
        uniqueId: 250729192312072,
        isGeoField: false,
        type: 'string',
        id: '1700038977695',
        originId: '1700038977695',
      },
      {
        roleType: 0,
        index: 2,
        format: {
          contentType: 'link',
        },
        aggrConf: {},
        isMetric: false,
        dimMetId: 1700038977696,
        location: 'dimensions',
        uniqueId: 250729192312097,
        isGeoField: false,
        type: 'string',
        id: '1700038977696',
        originId: '1700038977696',
      },
      {
        roleType: 0,
        index: 3,
        format: {},
        aggrConf: {},
        isMetric: false,
        dimMetId: 1700038977644,
        location: 'dimensions',
        uniqueId: 250729192312122,
        isGeoField: false,
        type: 'string',
        id: '1700038977644',
        originId: '1700038977644',
      },
      {
        roleType: 0,
        index: 4,
        format: {
          contentType: 'link',
        },
        aggrConf: {},
        isMetric: false,
        dimMetId: 1700038977689,
        location: 'dimensions',
        uniqueId: 250729192312149,
        isGeoField: false,
        type: 'string',
        id: '1700038977689',
        originId: '1700038977689',
      },
      {
        roleType: 0,
        index: 5,
        format: {},
        aggrConf: {},
        isMetric: false,
        dimMetId: 1700038979296,
        location: 'dimensions',
        uniqueId: 250729192312178,
        isGeoField: false,
        type: 'string',
        id: '1700038979296',
        originId: '1700038979296',
      },
      {
        roleType: 0,
        index: 6,
        format: {},
        aggrConf: {},
        isMetric: false,
        dimMetId: 1700038981733,
        location: 'dimensions',
        uniqueId: 250729192312295,
        isGeoField: false,
        type: 'string',
        id: '1700038981733',
        originId: '1700038981733',
      },
      {
        roleType: 0,
        index: 7,
        format: {},
        aggrConf: {},
        isMetric: false,
        dimMetId: 1700038977681,
        location: 'dimensions',
        uniqueId: 250729192312322,
        isGeoField: false,
        type: 'string',
        id: '1700038977681',
        originId: '1700038977681',
      },
      {
        roleType: 0,
        index: 8,
        format: {},
        aggrConf: {},
        isMetric: false,
        dimMetId: 1700038977662,
        location: 'dimensions',
        uniqueId: 250729192312388,
        isGeoField: false,
        type: 'string',
        id: '1700038977662',
        originId: '1700038977662',
      },
      {
        roleType: 0,
        index: 9,
        format: {},
        aggrConf: {},
        isMetric: false,
        dimMetId: 1700038977658,
        location: 'dimensions',
        uniqueId: 250729192312351,
        isGeoField: false,
        type: 'string',
        id: '1700038977658',
        originId: '1700038977658',
      },
    ],
    parameters: [],
    sizes: [],
    whereList: [
      {
        roleType: 0,
        index: 0,
        unremovable: false,
        format: {
          displayName: '组合筛选',
        },
        aggrConf: {},
        notJoinQuery: false,
        dimMetId: 250729192312417,
        filter: {
          children: [
            {
              roleType: 0,
              filter: {
                op: 'last',
                option: {
                  isReportFilter: false,
                  dateMode: 'relative',
                  isWhereInAggr: true,
                },
                val: [1],
                valOption: {
                  datetimeUnit: 'month',
                  hourSharp: true,
                  until: 'yesterday',
                  anchorOffset: 0,
                },
              },
              unremovable: true,
              name: '开播日期',
              format: {},
              aggrConf: {},
              dimMetId: 1700038977644,
              disableMenuKey: ['addOrFilter', 'subFilter'],
              preRelation: 'and',
              location: 'whereList',
              uniqueId: 250729192312446,
              originId: '1700038977644',
              showEditComponent: true,
              inCombinationPill: true,
              isMetric: false,
              id: '1700038977644',
              dataSetId: 2211620,
            },
          ],
          op: 'and',
        },
        location: 'whereList',
        uniqueId: 250729192312417,
        highlight: false,
        pillType: 'combination_filter',
        showEditComponent: false,
        nameIndex: 1,
        id: '250729192312417',
        originId: '250729192312417',
      },
      {
        roleType: 0,
        index: 1,
        name: '开播抖音号',
        format: {
          contentType: 'link',
        },
        aggrConf: {},
        dataSetId: 2211620,
        dimMetId: 1700038977695,
        filter: {
          op: 'not like',
          option: {
            isReportFilter: false,
            filterPattern: 'Condition',
          },
          val: ['89020762080', '55277644120'],
          valOption: {
            fuzzyLikePattern: 'Contain',
          },
        },
        isRequired: false,
        preRelation: 'and',
        location: 'whereList',
        uniqueId: 250729203208051,
        showEditComponent: false,
        isMetric: false,
        id: '1700038977695',
        originId: '1700038977695',
      },
    ],
    whiteList: [],
    measures: [
      {
        roleType: 1,
        index: 0,
        format: {
          numFormat: {
            precisionType: 'significantDecimal',
            auto: true,
            kSep: true,
            precision: 4,
            type: 'digit',
            unit: null,
          },
          dataTypeName: 'int',
        },
        aggrConf: {},
        isMetric: false,
        dimMetId: 1700039098260,
        location: 'measures',
        uniqueId: 250729192312258,
        isGeoField: false,
        type: 'string',
        id: '1700039098260',
        originId: '1700039098260',
      },
    ],
    periodCompare: [],
    display: {
      conf: {
        bodyFontUnderline: false,
        fixedIndex: -1,
        headerFontColor: '#1B1F23',
        headerColor: '#EEF1F5',
        bodyFontSize: 12,
        pageSize: 20,
        headerFontUnderline: false,
        colPadding: null,
        gridLineFrameWidth: 1,
        specialValue: {
          measures: 'bracketTxt',
          dimensions: 'null',
        },
        headerFontSize: 12,
        headerSubTitleFontSize: 12,
        alternateRowColor: '#FBFBFC',
        headerBackground: true,
        compact: false,
        rowPadding: null,
        headerSubTitleFontItalic: false,
        gridLineFrameStyle: 'solid',
        alignMeasure: 'right',
        headerSubTitleFontUnderline: false,
        autoWrap: false,
        gridLineVerticalWidth: 1,
        version: 33,
        tableStyle: 'standard',
        pagination: false,
        gridLineHorizontalColor: null,
        customFields: {
          enable: true,
        },
        gridLineHorizontal: true,
        headerFontItalic: false,
        gridLineFrame: true,
        bodyFontItalic: false,
        colSpaceMode: 'tight',
        gridLineVerticalColor: null,
        bodyFontColor: '#141414',
        rowSpaceMode: 'loose',
        gridLineFrameColor: null,
        alternateRow: true,
        gridLineHorizontalStyle: 'solid',
        headerFontBold: true,
        loadPartialData: true,
        transpose: false,
        lineNumber: false,
        hideHeader: false,
        compactDirection: 'horizontal',
        gridLineColor: '#E1E4E8',
        gridLineVerticalStyle: 'solid',
        sortable: false,
        measureFirst: false,
        columnWidth: [],
        headerSubTitleFontColor: 'rgba(20, 20, 20, 0.45)',
        headerSubTitleFontBold: false,
        gridLineHorizontalWidth: 1,
        headerMenu: true,
        alignDimension: 'left',
        hoverHighlight: 'row',
        bodyFontBold: false,
        gridLineVertical: true,
        display: 'standard',
      },
      type: 'table',
      queryType: 'table',
      enableAdvisor: true,
    },
    cache: {
      enable: true,
      cacheVersion: 'V1',
      expire: 300,
    },
    colors: [],
    extensions: {
      data: {},
      list: [],
      protocolVersion: 1,
    },
    drill: [],
    tableCalculation: {
      rules: [],
    },
    referenceLine: [],
    realMetricTableRouteConfig: {
      isRealMetricQuery: false,
    },
    subMeasures: [],
    annotation: {
      hash: 'd751713988987e9331980363e24189ce',
    },
    columns: [],
  },
  display: {
    conf: {
      bodyFontUnderline: false,
      fixedIndex: -1,
      headerFontColor: '#1B1F23',
      headerColor: '#EEF1F5',
      bodyFontSize: 12,
      pageSize: 20,
      headerFontUnderline: false,
      colPadding: null,
      gridLineFrameWidth: 1,
      specialValue: {
        measures: 'bracketTxt',
        dimensions: 'null',
      },
      headerFontSize: 12,
      headerSubTitleFontSize: 12,
      alternateRowColor: '#FBFBFC',
      headerBackground: true,
      compact: false,
      rowPadding: null,
      headerSubTitleFontItalic: false,
      gridLineFrameStyle: 'solid',
      alignMeasure: 'right',
      headerSubTitleFontUnderline: false,
      autoWrap: false,
      gridLineVerticalWidth: 1,
      version: 33,
      tableStyle: 'standard',
      pagination: false,
      gridLineHorizontalColor: null,
      customFields: {
        enable: true,
      },
      gridLineHorizontal: true,
      headerFontItalic: false,
      gridLineFrame: true,
      bodyFontItalic: false,
      colSpaceMode: 'tight',
      gridLineVerticalColor: null,
      bodyFontColor: '#141414',
      rowSpaceMode: 'loose',
      gridLineFrameColor: null,
      alternateRow: true,
      gridLineHorizontalStyle: 'solid',
      headerFontBold: true,
      loadPartialData: true,
      transpose: false,
      lineNumber: false,
      hideHeader: false,
      compactDirection: 'horizontal',
      gridLineColor: '#E1E4E8',
      gridLineVerticalStyle: 'solid',
      sortable: false,
      measureFirst: false,
      columnWidth: [],
      headerSubTitleFontColor: 'rgba(20, 20, 20, 0.45)',
      headerSubTitleFontBold: false,
      gridLineHorizontalWidth: 1,
      headerMenu: true,
      alignDimension: 'left',
      hoverHighlight: 'row',
      bodyFontBold: false,
      gridLineVertical: true,
      display: 'standard',
    },
    type: 'table',
    queryType: 'table',
    enableAdvisor: true,
    fieldsFormat: {
      '1700038977648': {},
      '1700038977695': {
        contentType: 'link',
      },
      '1700038977696': {
        contentType: 'link',
      },
      '1700038977644': {},
      '1700038977689': {
        contentType: 'link',
      },
      '1700038979296': {},
      '1700038981733': {},
      '1700038977681': {},
      '1700038977662': {},
      '1700038977658': {},
      '1700039098260': {
        numFormat: {
          precisionType: 'significantDecimal',
          auto: true,
          kSep: true,
          precision: 4,
          type: 'digit',
          unit: null,
        },
        dataTypeName: 'int',
      },
    },
  },
  originalSchema: {
    rows: [],
    reportFilterConfig: {
      structType: 'LeftRight',
      layoutSize: 'Normal',
    },
    dimensions: [
      {
        roleType: 0,
        index: 0,
        format: {},
        aggrConf: {},
        isMetric: false,
        dimMetId: 1700038977648,
        location: 'dimensions',
        uniqueId: 250729192312039,
        isGeoField: false,
        type: 'string',
        id: '1700038977648',
        originId: '1700038977648',
      },
      {
        roleType: 0,
        index: 1,
        format: {
          contentType: 'link',
        },
        aggrConf: {},
        isMetric: false,
        dimMetId: 1700038977695,
        location: 'dimensions',
        uniqueId: 250729192312072,
        isGeoField: false,
        type: 'string',
        id: '1700038977695',
        originId: '1700038977695',
      },
      {
        roleType: 0,
        index: 2,
        format: {
          contentType: 'link',
        },
        aggrConf: {},
        isMetric: false,
        dimMetId: 1700038977696,
        location: 'dimensions',
        uniqueId: 250729192312097,
        isGeoField: false,
        type: 'string',
        id: '1700038977696',
        originId: '1700038977696',
      },
      {
        roleType: 0,
        index: 3,
        format: {},
        aggrConf: {},
        isMetric: false,
        dimMetId: 1700038977644,
        location: 'dimensions',
        uniqueId: 250729192312122,
        isGeoField: false,
        type: 'string',
        id: '1700038977644',
        originId: '1700038977644',
      },
      {
        roleType: 0,
        index: 4,
        format: {
          contentType: 'link',
        },
        aggrConf: {},
        isMetric: false,
        dimMetId: 1700038977689,
        location: 'dimensions',
        uniqueId: 250729192312149,
        isGeoField: false,
        type: 'string',
        id: '1700038977689',
        originId: '1700038977689',
      },
      {
        roleType: 0,
        index: 5,
        format: {},
        aggrConf: {},
        isMetric: false,
        dimMetId: 1700038979296,
        location: 'dimensions',
        uniqueId: 250729192312178,
        isGeoField: false,
        type: 'string',
        id: '1700038979296',
        originId: '1700038979296',
      },
      {
        roleType: 0,
        index: 6,
        format: {},
        aggrConf: {},
        isMetric: false,
        dimMetId: 1700038981733,
        location: 'dimensions',
        uniqueId: 250729192312295,
        isGeoField: false,
        type: 'string',
        id: '1700038981733',
        originId: '1700038981733',
      },
      {
        roleType: 0,
        index: 7,
        format: {},
        aggrConf: {},
        isMetric: false,
        dimMetId: 1700038977681,
        location: 'dimensions',
        uniqueId: 250729192312322,
        isGeoField: false,
        type: 'string',
        id: '1700038977681',
        originId: '1700038977681',
      },
      {
        roleType: 0,
        index: 8,
        format: {},
        aggrConf: {},
        isMetric: false,
        dimMetId: 1700038977662,
        location: 'dimensions',
        uniqueId: 250729192312388,
        isGeoField: false,
        type: 'string',
        id: '1700038977662',
        originId: '1700038977662',
      },
      {
        roleType: 0,
        index: 9,
        format: {},
        aggrConf: {},
        isMetric: false,
        dimMetId: 1700038977658,
        location: 'dimensions',
        uniqueId: 250729192312351,
        isGeoField: false,
        type: 'string',
        id: '1700038977658',
        originId: '1700038977658',
      },
    ],
    parameters: [],
    sizes: [],
    whereList: [
      {
        roleType: 0,
        index: 0,
        unremovable: false,
        format: {
          displayName: '组合筛选',
        },
        aggrConf: {},
        notJoinQuery: false,
        dimMetId: 250729192312417,
        filter: {
          children: [
            {
              roleType: 0,
              filter: {
                op: 'last',
                option: {
                  isReportFilter: false,
                  dateMode: 'relative',
                  isWhereInAggr: true,
                },
                val: [1],
                valOption: {
                  datetimeUnit: 'month',
                  hourSharp: true,
                  until: 'yesterday',
                  anchorOffset: 0,
                },
              },
              unremovable: true,
              name: '开播日期',
              format: {},
              aggrConf: {},
              dimMetId: 1700038977644,
              disableMenuKey: ['addOrFilter', 'subFilter'],
              preRelation: 'and',
              location: 'whereList',
              uniqueId: 250729192312446,
              originId: '1700038977644',
              showEditComponent: true,
              inCombinationPill: true,
              isMetric: false,
              id: '1700038977644',
              dataSetId: 2211620,
            },
          ],
          op: 'and',
        },
        location: 'whereList',
        uniqueId: 250729192312417,
        highlight: false,
        pillType: 'combination_filter',
        showEditComponent: false,
        nameIndex: 1,
        id: '250729192312417',
        originId: '250729192312417',
      },
      {
        roleType: 0,
        index: 1,
        name: '开播抖音号',
        format: {
          contentType: 'link',
        },
        aggrConf: {},
        dataSetId: 2211620,
        dimMetId: 1700038977695,
        filter: {
          op: 'not like',
          option: {
            isReportFilter: false,
            filterPattern: 'Condition',
          },
          val: ['89020762080', '55277644120'],
          valOption: {
            fuzzyLikePattern: 'Contain',
          },
        },
        isRequired: false,
        preRelation: 'and',
        location: 'whereList',
        uniqueId: 250729203208051,
        showEditComponent: false,
        isMetric: false,
        id: '1700038977695',
        originId: '1700038977695',
      },
    ],
    whiteList: [],
    measures: [
      {
        roleType: 1,
        index: 0,
        format: {
          numFormat: {
            precisionType: 'significantDecimal',
            auto: true,
            kSep: true,
            precision: 4,
            type: 'digit',
            unit: null,
          },
          dataTypeName: 'int',
        },
        aggrConf: {},
        isMetric: false,
        dimMetId: 1700039098260,
        location: 'measures',
        uniqueId: 250729192312258,
        isGeoField: false,
        type: 'string',
        id: '1700039098260',
        originId: '1700039098260',
      },
    ],
    periodCompare: [],
    display: {
      conf: {
        bodyFontUnderline: false,
        fixedIndex: -1,
        headerFontColor: '#1B1F23',
        headerColor: '#EEF1F5',
        bodyFontSize: 12,
        pageSize: 20,
        headerFontUnderline: false,
        colPadding: null,
        gridLineFrameWidth: 1,
        specialValue: {
          measures: 'bracketTxt',
          dimensions: 'null',
        },
        headerFontSize: 12,
        headerSubTitleFontSize: 12,
        alternateRowColor: '#FBFBFC',
        headerBackground: true,
        compact: false,
        rowPadding: null,
        headerSubTitleFontItalic: false,
        gridLineFrameStyle: 'solid',
        alignMeasure: 'right',
        headerSubTitleFontUnderline: false,
        autoWrap: false,
        gridLineVerticalWidth: 1,
        version: 33,
        tableStyle: 'standard',
        pagination: false,
        gridLineHorizontalColor: null,
        customFields: {
          enable: true,
        },
        gridLineHorizontal: true,
        headerFontItalic: false,
        gridLineFrame: true,
        bodyFontItalic: false,
        colSpaceMode: 'tight',
        gridLineVerticalColor: null,
        bodyFontColor: '#141414',
        rowSpaceMode: 'loose',
        gridLineFrameColor: null,
        alternateRow: true,
        gridLineHorizontalStyle: 'solid',
        headerFontBold: true,
        loadPartialData: true,
        transpose: false,
        lineNumber: false,
        hideHeader: false,
        compactDirection: 'horizontal',
        gridLineColor: '#E1E4E8',
        gridLineVerticalStyle: 'solid',
        sortable: false,
        measureFirst: false,
        columnWidth: [],
        headerSubTitleFontColor: 'rgba(20, 20, 20, 0.45)',
        headerSubTitleFontBold: false,
        gridLineHorizontalWidth: 1,
        headerMenu: true,
        alignDimension: 'left',
        hoverHighlight: 'row',
        bodyFontBold: false,
        gridLineVertical: true,
        display: 'standard',
      },
      type: 'table',
      queryType: 'table',
      enableAdvisor: true,
    },
    cache: {
      enable: true,
      cacheVersion: 'V1',
      expire: 300,
    },
    colors: [],
    extensions: {
      data: {},
      list: [],
      protocolVersion: 1,
    },
    drill: [],
    tableCalculation: {
      rules: [],
    },
    referenceLine: [],
    realMetricTableRouteConfig: {
      isRealMetricQuery: false,
    },
    subMeasures: [],
    annotation: {
      hash: 'd751713988987e9331980363e24189ce',
    },
    columns: [],
  },
};
