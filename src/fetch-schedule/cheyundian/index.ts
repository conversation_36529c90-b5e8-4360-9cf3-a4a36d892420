import { getCheYunDianCookieByBitable } from '../../utils/bitable.js';
import { defaultHeaders } from '../utils/config';
import { importTableData } from '../utils/import';
import { getTokenByCheyundian } from '../utils/token';
import { postTableBody } from './requestBody/postTableBody.js';

async function getPostListData(token) {
  const url =
    'https://aeolus.bytedance.com/aeolus/vqs/openApi/v2/vizQuery/query';
  const body = postTableBody;
  const response = await fetch(url, {
    method: 'POST',
    // @ts-ignore
    headers: {
      ...defaultHeaders,
      'App-Id': 1002649,
      'Content-Language': 'zh-CN',
      'Data-Format-Unit': 'auto',
      'Open-Api-Token': token,
    },
    body: JSON.stringify(body),
  });

  const data = await response.json();
  const postTableDataOrigin = data.data;

  // columnsKey 映射
  const columnsKey = {
    '250616164342037': 'postTitle',
    '250616164342072': 'publishTime',
    '250616164342291': '视频时长(秒)',
    '250616164342316': 'playCount',
    '250616164342341': 'commentCount',
    '250616164342350': 'diggCount',
    '250616164342375': 'shareCount',
    '250616174956089': 'postId',
  };

  const rows = postTableDataOrigin.vizData.datasets?.map((row) => {
    const key = Object.keys(row);
    return key.reduce((acc, curKey) => {
      acc[columnsKey[curKey]] = row[curKey];
      return acc;
    }, {});
  });

  return rows;
}

// 测试环境
// https://new-media-dev.xiaofeilun.cn/new-media-api/toyota/short-video/v2/import

const importPostTableData = (rowsData) =>
  importTableData(
    rowsData,
    'https://new-media.xiaofeilun.cn/new-media-api/toyota/short-video/v2/import',
    'Toyota短视频数据表',
    '车云店'
  );

async function main() {
  // 1. 从多维表格中的先获取车云店Cookie
  const cheYunDianCookie = await getCheYunDianCookieByBitable();
  // 2. 请求第一个接口获取请求接口需要的鉴权 Token
  const cheYunDianToken = await getTokenByCheyundian(cheYunDianCookie);
  // 使用 Token 来获取数据，发送给后端同步数据
  const postRowsData = await getPostListData(cheYunDianToken);
  await importPostTableData(postRowsData);
}

main();
