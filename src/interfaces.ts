// API 接口统一管理
import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { log } from 'crawlee';
import { crawlerManager, runCrawler } from './crawler.js';
import { dongcheyundianDataSync } from './fetch-schedule/dongcheyundian/index.js';

// 接口响应类型定义
export interface ApiResponse<T = any> {
  success: boolean;
  message?: string;
  data?: T;
  timestamp?: string;
}

export interface HealthCheckResponse {
  status: 'ok' | 'error';
  timestamp: string;
  uptime: number;
}

export interface CrawlerStatus {
  isRunning: boolean;
  lastRunTime?: string;
  nextRunTime?: string;
  runCount?: number;
  [key: string]: any;
}

export interface CrawlerStatusResponse extends ApiResponse<CrawlerStatus> {
  success: true;
  data: CrawlerStatus;
}

// HTTP 状态码枚举
export enum HttpStatusCode {
  OK = 200,
  CONFLICT = 409,
  INTERNAL_SERVER_ERROR = 500,
}

// 错误类型定义
export interface ApiError {
  code: string;
  message: string;
  details?: any;
}

/**
 * 健康检查接口
 */
export const healthCheckHandler = async (
  request: FastifyRequest,
  reply: FastifyReply
): Promise<HealthCheckResponse> => {
  return {
    status: 'ok',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
  };
};

/**
 * 获取爬虫状态接口
 */
export const getCrawlerStatusHandler = async (
  request: FastifyRequest,
  reply: FastifyReply
): Promise<CrawlerStatusResponse> => {
  const status = crawlerManager.getStatus();
  return {
    success: true,
    data: status,
  };
};

/**
 * 手动触发爬虫接口
 */
export const runCrawlerHandler = async (
  request: FastifyRequest,
  reply: FastifyReply
): Promise<ApiResponse> => {
  try {
    log.info('收到手动触发爬虫请求');

    // 检查是否已有爬虫在运行
    if (crawlerManager.isRunning()) {
      return reply.code(409).send({
        success: false,
        message: '爬虫正在运行中，请等待当前任务完成',
      });
    }

    // 异步运行爬虫，不阻塞响应
    runCrawler().catch((error) => {
      log.error('爬虫运行失败:', error);
    });

    return {
      success: true,
      message: '爬虫任务已启动',
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    log.error('启动爬虫失败:', { error });
    return reply.code(500).send({
      success: false,
      message: error instanceof Error ? error.message : '启动爬虫失败',
    });
  }
};

/**
 * 手动触发懂车云店数据同步接口
 */
export const dongcheyundianDataSyncHandler = async (
  request: FastifyRequest,
  reply: FastifyReply
): Promise<ApiResponse> => {
  try {
    log.info('收到手动触发懂车云店数据同步请求');

    dongcheyundianDataSync().catch((error) => {
      log.error('懂车云店数据同步失败:', error);
    });

    return {
      success: true,
      message: '懂车云店数据同步任务已启动',
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    log.error('懂车云店数据同步失败', { error });
    return reply.code(500).send({
      success: false,
      message: error instanceof Error ? error.message : '懂车云店数据同步失败',
    });
  }
};

/**
 * 注册所有 API 接口
 */
export const registerApiRoutes = (fastify: FastifyInstance) => {
  // 健康检查端点
  fastify.get('/health', healthCheckHandler);

  // 获取爬虫状态
  fastify.get('/api/crawler/status', getCrawlerStatusHandler);

  // 手动触发爬虫
  fastify.post('/api/crawler/run', runCrawlerHandler);

  // 手动触发懂车云店数据同步
  fastify.post('/api/dongcheyundian/datasync', dongcheyundianDataSyncHandler);
};
