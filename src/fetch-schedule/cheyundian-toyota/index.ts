import { clueTableBody } from './requestBody/clueTableBody.js';
import { getCheYunDianCookieByBitable } from '../../utils/bitable.js';
import { defaultHeaders } from '../utils/config';
import { importTableData } from '../utils/import';
import { getTokenByCheyundian } from '../utils/token';
import { liveTableBody } from './requestBody/liveTableBody.js';
import { postTableBody } from './requestBody/postTableBody.js';

async function getLiveListData(token) {
  const url =
    'https://aeolus.bytedance.com/aeolus/vqs/openApi/v2/vizQuery/query';
  const body = liveTableBody;
  const response = await fetch(url, {
    method: 'POST',
    // @ts-ignore
    headers: {
      ...defaultHeaders,
      'App-Id': 1002649,
      'Content-Language': 'zh-CN',
      'Data-Format-Unit': 'auto',
      'Open-Api-Token': token,
    },
    body: JSON.stringify(body),
  });

  const data = await response.json();
  const postTableDataOrigin = data.data;

  // columnsKey 映射
  const columnsKey = {
    '***************': 'roomId',
    '***************': 'showAccountId',
    '***************': 'title',
    '***************': 'liveDuration',
    '***************': 'liveClueCount',
    '***************': 'liveAdCost',
    '***************': 'liveDiggCount',
    '***************': 'liveCommentCount',
    '***************': 'liveShareCount',
    '***************': 'liveStartTime',
    '***************': 'liveEndTime',
  };

  const rows = postTableDataOrigin.vizData.datasets?.map((row) => {
    const key = Object.keys(row);
    return key.reduce((acc, curKey) => {
      if (curKey === '***************') {
        acc[columnsKey[curKey]] = row[curKey] * 60;
      } else {
        acc[columnsKey[curKey]] = row[curKey];
      }
      return acc;
    }, {});
  });

  return rows;
}

async function getPostListData(token) {
  const url =
    'https://aeolus.bytedance.com/aeolus/vqs/openApi/v2/vizQuery/query';
  const body = postTableBody;
  const response = await fetch(url, {
    method: 'POST',
    // @ts-ignore
    headers: {
      ...defaultHeaders,
      'App-Id': 1002649,
      'Content-Language': 'zh-CN',
      'Data-Format-Unit': 'auto',
      'Open-Api-Token': token,
    },
    body: JSON.stringify(body),
  });

  const data = await response.json();
  const postTableDataOrigin = data.data;

  // columnsKey 映射
  const columnsKey = {
    '***************': 'postId',
    '***************': 'title',
    '***************': 'showAccountId',
    '***************': 'publishTime',
    '***************': 'playCount',
    '***************': 'adCost',
    '***************': 'diggCount',
    '***************': 'commentCount',
    '***************': 'shareCount',
  };

  const rows = postTableDataOrigin.vizData.datasets?.map((row) => {
    const key = Object.keys(row);
    return key.reduce((acc, curKey) => {
      acc[columnsKey[curKey]] = row[curKey];
      return acc;
    }, {});
  });

  return rows;
}

async function getClueListData(token) {
  const url =
    'https://aeolus.bytedance.com/aeolus/vqs/openApi/v2/vizQuery/query';
  const body = clueTableBody;
  const response = await fetch(url, {
    method: 'POST',
    // @ts-ignore
    headers: {
      ...defaultHeaders,
      'App-Id': 1002649,
      'Content-Language': 'zh-CN',
      'Data-Format-Unit': 'auto',
      'Open-Api-Token': token,
    },
    body: JSON.stringify(body),
  });

  const data = await response.json();
  const postTableDataOrigin = data.data;

  // columnsKey 映射
  const columnsKey = {
    '***************': 'showAccountId',
    '***************': 'liveClueDistinctCount',
    '***************': 'totalClueDistinctCount',
    '***************': 'privateMsgClueDistinctCount',
    '***************': 'postClueDistinctCount',
    '***************': 'statDate',
  };

  const rows = postTableDataOrigin.vizData.datasets?.map((row) => {
    const key = Object.keys(row);
    return key.reduce((acc, curKey) => {
      acc[columnsKey[curKey]] = row[curKey];
      return acc;
    }, {});
  });

  return rows;
}

const importLiveTableData = (rowsData) =>
  importTableData(
    rowsData,
    'https://new-media-dev.xiaofeilun.cn/new-media-api/project/performance/data-report/live?projectId=71',
    'Toyota-[业绩考核]-直播数据表',
    '车云店'
  );

const importPostTableData = (rowsData) =>
  importTableData(
    rowsData,
    'https://new-media-dev.xiaofeilun.cn/new-media-api/project/performance/data-report/post?projectId=71',
    'Toyota-[业绩考核]-作品数据表',
    '车云店'
  );

const importClueTableData = (rowsData) =>
  importTableData(
    rowsData,
    'https://new-media-dev.xiaofeilun.cn/new-media-api/project/performance/data-report/user/clue?projectId=71',
    'Toyota-[业绩考核]-用户线索数据表',
    '车云店'
  );

async function main() {
  // 1. 从多维表格中的先获取车云店Cookie
  const cheYunDianCookie = await getCheYunDianCookieByBitable();
  // 2. 请求第一个接口获取请求接口需要的鉴权 Token
  const cheYunDianToken = await getTokenByCheyundian(cheYunDianCookie);
  // 使用 Token 来获取数据，发送给后端同步数据
  const liveRowsData = await getLiveListData(cheYunDianToken);
  const postRowsData = await getPostListData(cheYunDianToken);
  const clueRowsData = await getClueListData(cheYunDianToken);
  await Promise.all([
    importLiveTableData(liveRowsData),
    importPostTableData(postRowsData),
    importClueTableData(clueRowsData),
  ]);
}

main();
